
body {
    font-family: 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.8;
    margin: 0;
    background-color: #ffffff;
    color: #333;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    letter-spacing: 0.02em;
}

header {
    border-bottom: 1px solid #f0f0f0;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

nav {
    width: 100%;
    max-width: 700px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.nav-links {
    display: flex;
    align-items: center;
}

.nav-links a {
    margin-right: 20px;
    font-size: 0.95rem;
    font-weight: 400;
    padding: 5px 0;
    position: relative;
}

.nav-links a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 1px;
    bottom: 0;
    left: 0;
    background-color: #888;
    transition: width 0.3s ease;
}

.nav-links a:hover::after {
    width: 100%;
}

nav a {
    text-decoration: none;
    color: #333;
    transition: color 0.3s ease;
}

nav a:hover {
    color: #888;
}

h1 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 300;
    letter-spacing: 0.05em;
}

main {
    flex-grow: 1;
    width: 100%;
    max-width: 700px;
    margin: 0 auto 3rem;
    padding: 0 20px;
}

footer {
    text-align: center;
    padding: 2rem;
    font-size: 0.8rem;
    color: #aaa;
    border-top: 1px solid #f0f0f0;
    margin-top: 4rem;
}

.post-list {
    list-style: none;
    padding: 0;
}

.post-list li {
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #f0f0f0;
}

.post-list li:last-child {
    border-bottom: none;
}

.post-list a {
    text-decoration: none;
    color: #333;
    font-size: 1.4rem;
    font-weight: 400;
    transition: color 0.3s ease;
    display: block;
    margin-bottom: 0.5rem;
}

.post-list a:hover {
    color: #888;
}

.post-meta {
    color: #aaa;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    display: block;
}

.post-content {
    white-space: pre-wrap; /* This will respect newlines in your txt files */
    line-height: 1.8;
}

/* Search Box */
.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

#search-input {
    padding: 10px 15px;
    border: none;
    border-bottom: 1px solid #eee;
    font-size: 0.9rem;
    width: 200px;
    background-color: #fafafa;
    transition: all 0.3s ease;
    outline: none;
}

#search-input:focus {
    border-bottom: 1px solid #ccc;
    background-color: #fff;
    width: 250px;
}

#search-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    margin-left: 5px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}

#search-button:hover {
    background-color: #f0f0f0;
}

.search-icon {
    font-size: 1.2rem;
}

#search-results {
    position: absolute;
    top: 100%;
    right: 0;
    width: 300px;
    background: white;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    list-style: none;
    padding: 0;
    margin: 0;
    display: none; /* Hidden by default */
    z-index: 10;
    border-radius: 4px;
    overflow: hidden;
}

#search-results li {
    padding: 12px 15px;
    border-bottom: 1px solid #f5f5f5;
    transition: background-color 0.2s ease;
}

#search-results li:last-child {
    border-bottom: none;
}

#search-results li a {
    text-decoration: none;
    color: #333;
    display: block;
    font-size: 0.95rem;
    font-weight: 500;
    margin-bottom: 4px;
}

#search-results li:hover {
    background-color: #f9f9f9;
}

.search-preview {
    font-size: 0.8rem;
    color: #888;
    margin-top: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.more-results, .no-results {
    text-align: center;
    padding: 10px;
    font-size: 0.85rem;
    color: #aaa;
    font-style: italic;
    background-color: #fafafa;
}
