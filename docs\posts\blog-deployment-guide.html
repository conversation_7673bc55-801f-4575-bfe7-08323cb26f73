<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>博客文章部署完整指南</title>
    <link rel="stylesheet" href="../static/css/style.css">
</head>
<body>
    <header>
        <nav>
            <a href="../index.html"><h1>我的極簡博客</h1></a>
        </nav>
    </header>
    <main>
        <h1>博客文章部署完整指南</h1>
<div class="post-meta">發佈於 2025-07-30</div>
<div class="post-content">
    本文將詳細介紹如何在我的極簡博客系統中新增文章並部署到GitHub Pages的完整流程。

## 文章格式要求

在創建新文章時，請遵循以下格式：
- 第一行：文章標題
- 第二行：空白行
- 第三行開始：文章內容

文章檔案應保存為 `.txt` 格式，並放置在 `content/` 目錄中。

## 本地構建命令

### 1. 構建靜態網站
```
python build.py
```

這個命令會：
- 掃描 `content/` 目錄中的所有 `.txt` 文件
- 根據模板生成對應的HTML頁面
- 更新首頁的文章列表
- 將所有生成的檔案放入 `docs/` 目錄

### 2. 檢查構建結果（可選）
```
dir docs
dir docs\posts
```

## Git版本控制命令

### 1. 檢查當前狀態
```
git status
```

### 2. 添加所有更改
```
git add .
```

### 3. 提交更改
```
git commit -m "新增文章: [文章標題]"
```

### 4. 推送到GitHub
```
git push origin main
```

## 完整部署流程示例

假設您要新增一篇名為 `my-new-article.txt` 的文章：

```
# 步驟1: 構建網站
python build.py

# 步驟2: 檢查Git狀態
git status

# 步驟3: 添加所有更改
git add .

# 步驟4: 提交更改
git commit -m "新增文章: 我的新文章"

# 步驟5: 推送到GitHub
git push origin main
```

## 部署後驗證

推送完成後，您可以：
1. 訪問 https://co2sou.github.io/blog 查看更新
2. 檢查新文章是否出現在首頁列表中
3. 點擊文章鏈接確認內容顯示正確

## 注意事項

- GitHub Pages 部署通常需要幾分鐘時間
- 確保文章檔案使用UTF-8編碼
- 文章標題會自動成為頁面標題和URL的一部分
- 文章按修改時間排序，最新的會顯示在最前面

## 技術架構說明

本博客系統採用：
- Python構建腳本自動化處理
- 純靜態HTML輸出
- GitHub Pages免費託管
- 響應式CSS設計
- 極簡主義設計理念

這套流程確保了從寫作到發布的高效率，讓您可以專注於內容創作而不必擔心技術細節。
</div>

    </main>
    <footer>
        <p>&copy; 2025 我的博客</p>
    </footer>

</body>
</html>
