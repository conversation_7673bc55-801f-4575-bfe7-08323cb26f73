<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>博客文章部署完整指南</title>
    <link rel="stylesheet" href="../static/css/style.css">
</head>
<body>
    <header>
        <nav>
            <a href="https://co2sou.github.io/blog/"><h1>我的極簡博客</h1></a>
        </nav>
    </header>
    <main>
        <h1>博客文章部署完整指南</h1>
<div class="post-meta">發佈於 2025-07-30</div>
<div class="post-content">
    在創建新文章時，請遵循以下格式：
第一行：文章標題
第二行：空白行
第三行開始：文章內容

文章檔案應保存為 `.txt` 格式，並放置在 `content/` 目錄中。

步驟1: 構建網站
python build.py

步驟2: 檢查Git狀態
git status

步驟3: 添加所有更改
git add .

步驟4: 提交更改
git commit -m "新增文章: 我的新文章"

步驟5: 推送到GitHub
git push origin main

部署後驗證
訪問 https://co2sou.github.io/blog
</div>

    </main>
    <footer>
        <p>&copy; 2025 我的博客</p>
    </footer>

</body>
</html>
